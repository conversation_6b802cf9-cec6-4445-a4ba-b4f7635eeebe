# Password Manager - Desktop Application

Безопасный менеджер паролей для Windows с современным интерфейсом.

## Особенности

- 🔒 Безопасное хранение паролей с шифрованием
- 🎨 Современный темный интерфейс
- 💾 Локальное хранение данных (IndexedDB)
- 🖥️ Настольное приложение для Windows
- 📱 Адаптивный дизайн
- 🔄 Автоматическое резервное копирование
- 📋 Копирование паролей в буфер обмена
- 🏷️ Организация по категориям
- 📝 Заметки и документы
- 💳 Хранение банковских карт

## Системные требования

- Windows 10/11 (64-bit)
- 4 ГБ RAM (рекомендуется)
- 100 МБ свободного места на диске

## Установка и запуск

### Вариант 1: Готовые файлы (рекомендуется)

1. Скачайте один из файлов:
   - `Password Manager Setup.exe` - установщик
   - `PasswordManager-Portable-1.0.0.exe` - портативная версия

2. **Установщик**: Запустите и следуйте инструкциям
3. **Портативная версия**: Просто запустите exe файл

### Вариант 2: Сборка из исходного кода

#### Требования для сборки:
- Node.js 18+ (https://nodejs.org/)
- Git (опционально)

#### Шаги сборки:

1. **Клонируйте или скачайте проект**
   ```bash
   git clone <repository-url>
   cd password-manager
   ```

2. **Автоматическая сборка**
   ```bash
   # Запустите build.bat
   build.bat
   ```

3. **Ручная сборка**
   ```bash
   # Установите зависимости
   npm install
   
   # Соберите приложение
   npm run build-all
   ```

4. **Разработка**
   ```bash
   # Запуск в режиме разработки
   run-dev.bat
   # или
   npm run dev
   ```

## Структура проекта

```
password-manager/
├── components/          # React компоненты
├── utils/              # Утилиты (криптография, база данных)
├── assets/             # Ресурсы (иконки)
├── dist/               # Собранные файлы
├── electron-main.js    # Главный процесс Electron
├── electron-preload.js # Preload скрипт
├── package.json        # Конфигурация проекта
├── index.html          # Главная страница
├── app.js              # Основное приложение
├── styles.css          # Стили
├── build.bat           # Скрипт сборки
└── run-dev.bat         # Скрипт разработки
```

## Команды NPM

- `npm start` - Запуск приложения
- `npm run dev` - Запуск в режиме разработки
- `npm run build` - Сборка приложения
- `npm run build-win` - Сборка установщика для Windows
- `npm run build-portable` - Сборка портативной версии
- `npm run build-all` - Сборка всех версий

## Безопасность

- Все пароли шифруются перед сохранением
- Данные хранятся локально на вашем компьютере
- Мастер-пароль не передается по сети
- Автоматический выход по таймауту бездействия

## Использование

1. При первом запуске создайте мастер-пароль
2. Добавляйте пароли через кнопку "+"
3. Организуйте пароли по категориям
4. Используйте поиск для быстрого доступа
5. Копируйте пароли одним кликом

## Горячие клавиши

- `Ctrl+R` - Перезагрузить приложение
- `F12` - Открыть инструменты разработчика
- `F11` - Полноэкранный режим
- `Ctrl+Q` - Выход из приложения
- `Ctrl+Plus` - Увеличить масштаб
- `Ctrl+Minus` - Уменьшить масштаб
- `Ctrl+0` - Сбросить масштаб

## Устранение неполадок

### Приложение не запускается
- Убедитесь, что у вас Windows 10/11
- Проверьте, что файл не заблокирован антивирусом
- Запустите от имени администратора

### Ошибки при сборке
- Убедитесь, что установлен Node.js 18+
- Очистите кэш: `npm cache clean --force`
- Удалите node_modules и переустановите: `rm -rf node_modules && npm install`

### Проблемы с данными
- Данные хранятся в IndexedDB браузера
- При проблемах используйте функцию "Очистить данные"
- Создавайте резервные копии важных паролей

## Лицензия

MIT License - см. файл LICENSE

## Поддержка

Для получения поддержки создайте issue в репозитории проекта.
