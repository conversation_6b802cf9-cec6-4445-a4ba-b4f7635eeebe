<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="128" cy="128" r="120" fill="url(#grad1)" stroke="#1E293B" stroke-width="4"/>
  
  <!-- Shield shape -->
  <path d="M128 40 L180 70 L180 140 Q180 180 128 200 Q76 180 76 140 L76 70 Z" 
        fill="#FFFFFF" opacity="0.9"/>
  
  <!-- Lock icon -->
  <rect x="108" y="120" width="40" height="50" rx="4" fill="#1E40AF"/>
  <rect x="115" y="100" width="26" height="30" rx="13" fill="none" 
        stroke="#1E40AF" stroke-width="4"/>
  
  <!-- Key hole -->
  <circle cx="128" cy="140" r="6" fill="#FFFFFF"/>
  <rect x="125" y="140" width="6" height="15" fill="#FFFFFF"/>
</svg>
